{"packages": [{"name": "wpfactory/wpfactory-admin-menu", "version": "v1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wpcodefactory/wpfactory-admin-menu.git", "reference": "fe48c4100a6b436a3dd39308814887119063c140"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wpcodefactory/wpfactory-admin-menu/zipball/fe48c4100a6b436a3dd39308814887119063c140", "reference": "fe48c4100a6b436a3dd39308814887119063c140", "shasum": ""}, "require-dev": {"wp-cli/wp-cli-bundle": "*"}, "time": "2024-10-14T14:01:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/php/"]}, "scripts": {"wp": ["vendor/bin/wp"], "wp-create-pot": ["wp i18n make-pot src/php ./langs/wpfactory-admin-menu.pot --domain=wpfactory-admin-menu"], "wp-update-po": ["wp i18n update-po ./langs/wpfactory-admin-menu.pot"], "wp-make-mo": ["wp i18n make-mo ./langs/"], "translate": ["@composer run wp-create-pot", "@composer run wp-update-po", "@composer run wp-make-mo"]}, "support": {"source": "https://github.com/wpcodefactory/wpfactory-admin-menu/tree/v1.0.3", "issues": "https://github.com/wpcodefactory/wpfactory-admin-menu/issues"}, "install-path": "../wpfactory/wpfactory-admin-menu"}, {"name": "wpfactory/wpfactory-cross-selling", "version": "v1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wpcodefactory/wpfactory-cross-selling.git", "reference": "d37c73013e897d3ce3db05b97046b30fdd2fd403"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wpcodefactory/wpfactory-cross-selling/zipball/d37c73013e897d3ce3db05b97046b30fdd2fd403", "reference": "d37c73013e897d3ce3db05b97046b30fdd2fd403", "shasum": ""}, "require": {"wpfactory/wpfactory-admin-menu": "*"}, "require-dev": {"wp-cli/wp-cli-bundle": "*"}, "time": "2024-10-31T15:55:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/php/"]}, "scripts": {"wp": ["vendor/bin/wp"], "wp-create-pot": ["wp i18n make-pot src/php ./langs/wpfactory-cross-selling.pot --domain=wpfactory-cross-selling"], "wp-update-po": ["wp i18n update-po ./langs/wpfactory-cross-selling.pot"], "wp-make-mo": ["wp i18n make-mo ./langs/"], "translate": ["@composer run wp-create-pot", "@composer run wp-update-po", "@composer run wp-make-mo"]}, "support": {"source": "https://github.com/wpcodefactory/wpfactory-cross-selling/tree/v1.0.3", "issues": "https://github.com/wpcodefactory/wpfactory-cross-selling/issues"}, "install-path": "../wpfactory/wpfactory-cross-selling"}], "dev": true, "dev-package-names": []}