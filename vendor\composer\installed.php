<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'aef1d7af0b0120877137ad714e660e2ca4fc444c',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'aef1d7af0b0120877137ad714e660e2ca4fc444c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wpfactory/wpfactory-admin-menu' => array(
            'pretty_version' => 'v1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'fe48c4100a6b436a3dd39308814887119063c140',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wpfactory/wpfactory-admin-menu',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wpfactory/wpfactory-cross-selling' => array(
            'pretty_version' => 'v1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'd37c73013e897d3ce3db05b97046b30fdd2fd403',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wpfactory/wpfactory-cross-selling',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
