=== Back Button Widget ===
Contributors: wpcodefactory, algor<PERSON><PERSON><PERSON>, anbinder, karzin, omardabbas
Tags: back button widget, back button, back, go back
Requires at least: 4.4
Tested up to: 6.8
Stable tag: 1.7.0
License: GNU General Public License v3.0
License URI: http://www.gnu.org/licenses/gpl-3.0.html

A simple & customizable back button, add it to any WordPress page using shortcode or widget for enhanced user navigation experience and site accessibility.

== Description ==

> “GREAT! WORKS PERFECT AND WONDERFUL SUPPORT: I needed a simple way to allow my clients to go back (BACK TO RESULTS) using my IDX real estate pages. The Back Button worked perfect and was easy to use. The Developer also was very kind to provide me with some additional code to match the rest of my web site colors.” – ⭐⭐⭐⭐⭐ [norwood451](https://wordpress.org/support/topic/great-works-perfect-and-wonderful-support/)

[Main Page](https://wpfactory.com/item/back-button-widget-wordpress-plugin/?utm_source=wporg&utm_medium=organic&utm_campaign=readme "Main Page") | [Support Forum](https://wpfactory.com/support/item/back-button-widget-wordpress-plugin/?utm_source=wporg&utm_medium=organic&utm_campaign=readme "Support Forum") | [Documentation & How to](https://wpfactory.com/docs/back-button-widget-wordpress-plugin/?utm_source=wporg&utm_medium=organic&utm_campaign=readme "Documentation & How to")


Navigating through a website should be smooth sailing, right?

Well, that's where our Back Button plugin steps in. It's all about simplicity and convenience. With just a shortcode or widget, users can add a customizable Back button to their WordPress site, a simple & handy tool for improving navigation and enhancing the user experience, making it smooth for visitors to explore your content and navigate your site easily.

## 🚀 Main Features: FREE Version##

### 🚀 Add Back Button Using WordPress Widget ###

As simple as it sounds, you can add & customize your back button with WP Widget, whenever they are supported on your theme (sidebar, header, footer, menu, WooCommerce sidebar, etc).

It comes with multiple options to customize the appearance of your Back button widget:

* Give it a unique title
* Add text before button
* Add text after button
* Change button text (label)
* Give it a class name (to customize it your own)
* Change button style (color) using simple HTML styling options
* Select to make the button type to be a button or simple text
* Select button JavaScript function to be "back" or "go(-1)"

### 🚀 Add Back Button Using Shortcode or PHP Function ###

If you're more into customizing the button through shortcode, the plugin allows adding the button by using `[alg_back_button]` or `echo alg_back_button( 'Back' );` PHP function.

### 🚀 Font Awesome Support ###

You can include any icon in your back button using simple addition to the shortcode, like `[alg_back_button fa="fas fa-angle-double-left"]`


___
## ❤️ User Testimonials: See What Others Are Saying!##

> “Great plugin with excellent support: This is a “must have” plugin for every WordPress site, thanks to the developer for creating this plugin, and for providing excellent and fast support.” – ⭐⭐⭐⭐⭐ [carsten-lund](https://wordpress.org/support/topic/great-plugin-with-excellent-support-93/)

> “Great Plugin!!!: I had a slight issue that was mainly my own error, but the author responded very quickly and all was resolved on this wonderfully simple and very useful plugin.” – ⭐⭐⭐⭐⭐ [kastenmeier](https://wordpress.org/support/topic/great-plugin-27772/)

> “Great plugin with excellent support: This is a “must have” plugin for every WordPress site, thanks to the developer for creating this plugin, and for providing excellent and fast support.” – ⭐⭐⭐⭐⭐ [carsten-lund](https://wordpress.org/support/topic/great-plugin-with-excellent-support-93/)

> “Awesome plugin: It just works perfect!” – ⭐⭐⭐⭐⭐ [anticosti](https://wordpress.org/support/topic/awesome-plugin-4434/)

## 🏆 Do More: PRO Version##

The premium version of [Back Button Widget](https://wpfactory.com/item/back-button-widget-wordpress-plugin/?utm_source=wporg&utm_medium=organic&utm_campaign=readme) plugin comes with more features, like:

### 🏆 Add Back Button to WordPress Menus ###

You can add the back button to any menu location (based on your theme options) or your own menus that you've created.
The options allow you to customize the button & item template for the back button

### 🏆 Add Back Button as Floating Button ###

Add the back function as a floating button anywhere on your page, select from bottom (right & left), and top (right & left) locations where the button should appear.

This comes with basic CSS options to change horizontal & vertical margins.

___
## 💯 Why WPFactory?##

* **Experience You Can Trust:** Over a decade in the business
* **Wide Plugin Selection:** Offering 65+ unique and powerful plugins
* **Highly-Rated Support:** Backed by hundreds of 5-star reviews
* **Expert Team:** Dedicated developers and technical support at your service

___
## What's Next? Discover More Plugins by WPFactory ##

WPFactory has a diverse range of plugins tailored to enhance your experience:

* [**Rename Media Files: Improve Your WordPress SEO**](https://wpfactory.com/item/file-renaming-on-upload-wordpress-plugin/ "**Rename Media Files: Improve Your WordPress SEO**"): Enhance SEO and organize media effortlessly with Rename Media Files WordPress Plugin. Fix upload issues, santize & optimize filenames, and improve SEO seamlessly. (**[Free version](https://wordpress.org/plugins/file-renaming-on-upload/ "Free version")**)

* [**Download Plugins and Themes from WordPress**](https://wpfactory.com/item/download-plugins-and-themes-from-dashboard-wordpress-plugin/?utm_source=wporg&utm_medium=organic&utm_campaign=readme "**Download Plugins and Themes from WordPress**"): Download installed plugins and themes in ZIP files directly from your WordPress admin dashboard, download any or all plugins & themes without FTP or cPanel access (**[Free version](https://wordpress.org/plugins/download-plugins-dashboard/ "Free version")**)

* [**Slugs Manager: Delete Old Permalinks from WordPress Database**](https://wpfactory.com/item/slugs-manager-wordpress-plugin/?utm_source=wporg&utm_medium=organic&utm_campaign=readme "**Slugs Manager: Delete Old Permalinks from WordPress Database**"): Scan & remove old or outdated slugs (permalinks) in Wordpress, keep your database optimized & your URLs SEO-friendly (**[Free version](https://wordpress.org/plugins/remove-old-slugspermalinks/ "Free version")**)

* [**Automated Order Status Controller for WooCommerce**](https://wpfactory.com/item/order-status-rules-for-woocommerce/?utm_source=wporg&utm_medium=organic&utm_campaign=readme "**Automated Order Status Controller for WooCommerce**"): Change order statuses programmatically based on a wide range of conditions, like time intervals, user roles and more! (**[Free version](https://wordpress.org/plugins/order-status-rules-for-woocommerce/ "Free version")**)

* [**Custom Order Status for WooCommerce**](https://wpfactory.com/item/order-status-for-woocommerce/?utm_source=wporg&utm_medium=organic&utm_campaign=readme "**Custom Order Status for WooCommerce**"): Create & manage unlimited number of custom statuses, to reflect your business desires & needs. (**[Free version](https://wordpress.org/plugins/order-status-for-woocommerce/ "Free version")**)

* [**Free Shipping Amount Label & Progress Bar for WooCommerce**](https://wpfactory.com/item/amount-left-free-shipping-woocommerce/?utm_source=wporg&utm_medium=organic&utm_campaign=readme "**Free Shipping Amount Label & Progress Bar for WooCommerce**"): Encourage higher spending by offering free shipping based on amount, with a progress bar for customers. (**[Free version](https://wordpress.org/plugins/amount-left-free-shipping-woocommerce/ "Free version")**)

== Installation ==

**Follow these simplified steps to get your plugin up and running:**

**From the WordPress Admin Panel:**
1. Navigate to “Plugins” > “Add New”.
2. Use the search bar and find the plugin using the exact name.
3. Click “Install Now” for the desired plugin.
4. Once the installation is finished, and click “Activate”.

**Manual Installation Using FTP:**
1. Download the desired plugin from WordPress.org.
2. Using your preferred FTP client, upload the entire plugin folder to the /wp-content/plugins/ directory of your WordPress installation.
3. Go to “Plugins” > “Installed Plugins” in your dashboard and click “Activate”.

**Manual download & upload from the WordPress Admin Panel:**
1. Download the desired plugin in a ZIP format.
2. On your site, navigate to “Plugins” > “Add New” and click the “Upload Plugin” button.
3. Choose the downloaded plugin file and click “Install Now.”
4. After the installation is complete, click “Activate”.

**Post-Activation:**
Once activated, access the plugin's settings by navigating to the “WPFactory” menu and look for the relevant tab.

== Changelog ==

= 1.7.0 - 10/05/2025 =
* Dev - Security - Escape output.
* Dev - Plugin settings moved to the "WPFactory" menu.
* Dev - "Recommendations" added.
* Dev - "Key Manager" added.
* Dev - Code refactoring.
* Dev - Coding standards improved.
* Tested up to: 6.8.

= 1.6.8 - 05/09/2024 =
* Tested up to: 6.6.

= 1.6.7 - 09/05/2024 =
* Dev - PHP 8.2 compatibility - "Creation of dynamic property is deprecated" notice fixed.
* Dev - Admin settings descriptions updated.
* Readme.txt - Minor fixes.

= 1.6.6 - 08/05/2024 =
* Fix - `ALG_BACK_BUTTON_WIDGET_VERSION` constant updated.

= 1.6.5 - 08/05/2024 =
* Tested up to: 6.5.
* Readme.txt - Plugin description, tags updated.

= 1.6.4 - 28/09/2023 =
* Dev - Escaping output.

= 1.6.3 - 23/09/2023 =
* Tested up to: 6.3.
* WP plugin logo, banner updated.

= 1.6.2 - 08/08/2023 =
* Contributors updated.

= 1.6.1 - 13/04/2023 =
* Dev - Shortcode - `fa_template` attribute added (defaults to `%icon%`).
* Dev - Shortcode - Optional `before` and `after` attributes added.
* Tested up to: 6.2.

= 1.6.0 - 03/11/2022 =
* Dev - Plugin is initialized on the `plugins_loaded` action now.
* Tested up to: 6.1.
* Readme.txt updated.
* Deploy script added.

= 1.5.3 - 02/02/2022 =
* Dev - "Font Awesome" options added ("Load" and "URL").
* Tested up to: 5.9.

= 1.5.2 - 14/07/2021 =
* Dev - Shortcode - `show_on_url_param` and `show_on_url_param_value` attributes added.

= 1.5.1 - 12/07/2021 =
* Dev - Floating Button - "Set as shortcode" options added.

= 1.5.0 - 12/07/2021 =
* Dev - Widget - "Hide on URL param" options added.
* Dev - Shortcode - `hide_on_url_param` and `hide_on_url_param_value` attributes added.
* Dev - Code refactoring.

= 1.4.0 - 19/04/2021 =
* Fix - Menu Options - Replace URL - Option fixed.
* Dev - "Floating Button Options" added.
* Dev - Settings - Major code refactoring.
* Tested up to: 5.7.

= 1.3.0 - 01/03/2021 =
* Dev - "Menu Options" added.
* Dev - `type` - `href` option added.
* Dev - Localisation - `load_plugin_textdomain` moved to the `init` action.

= 1.2.4 - 11/12/2020 =
* Dev - Shortcodes - `[alg_back_button]` - `fa` (Font Awesome) attribute added.
* Tested up to: 5.6.

= 1.2.3 - 31/08/2020 =
* Tested up to: 5.5.

= 1.2.2 - 20/03/2020 =
* Dev - Shortcodes - `[alg_back_button]` - Language (i.e., translation) attributes (`lang` and `not_lang_text`) added.
* Dev - Admin action links text updated.

= 1.2.1 - 03/03/2020 =
* Dev - Admin action links added.
* Dev - Plugin version is saved in site options now.

= 1.2.0 - 20/02/2020 =
* Dev - "Hide on front page" option added.
* Dev - Code refactoring.
* Link to Pro version added.

= 1.1.2 - 26/11/2019 =
* Dev - Minor code refactoring.
* Tested up to: 5.3.

= 1.1.1 - 21/06/2019 =
* Dev - "Button JS function" option added.

= 1.1.0 - 05/06/2019 =
* Dev - Shortcodes are now applied on button label (and `[alg_back_button_translate]` shortcode added).
* Tested up to: 5.2.

= 1.0.1 - 01/03/2019 =
* Dev - Widget descriptions updated.
* Dev - Standard classes added to buttons.
* Dev - POT file added.
* Dev - readme.txt updated.

= 1.0.0 - 02/08/2017 =
* Initial Release.

== Upgrade Notice ==

= 1.0.0 =
This is the first release of the plugin.
