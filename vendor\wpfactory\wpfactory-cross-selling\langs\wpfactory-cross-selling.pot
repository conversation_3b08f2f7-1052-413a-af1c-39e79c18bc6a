msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-09-23T03:49:09+02:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: wpfactory-cross-selling\n"

#: class-products.php:46
msgid "Wishlist for WooCommerce"
msgstr ""

#: class-products.php:47
msgid "Encourage More Purchases by Offering Easy Multi-Wishlist Creation and Sharing Features."
msgstr ""

#: class-products.php:56
msgid "Min Max Default Quantity for WooCommerce"
msgstr ""

#: class-products.php:57
msgid "Control Product Quantities and make shopping perfectly tailored to your store's needs."
msgstr ""

#: class-products.php:66
msgid "Cost of Goods Sold (COGS): Cost & Profit Calculator for WooCommerce"
msgstr ""

#: class-products.php:67
msgid "Understand your profits by accurately tracking costs. Make smarter decisions for your business and maximize your store's profitability with ease."
msgstr ""

#: class-products.php:76
msgid "Maximum Products per User for WooCommerce"
msgstr ""

#: class-products.php:77
msgid "Set maximum quantities based on your store's needs. Keep things fair, control stock, and manage sales your way!"
msgstr ""

#: class-products.php:86
msgid "Order Minimum/Maximum Amount for WooCommerce"
msgstr ""

#: class-products.php:87
msgid "Control every order with customizable limits to optimize your sales strategy."
msgstr ""

#: class-products.php:96
msgid "EU VAT Manager for WooCommerce"
msgstr ""

#: class-products.php:97
msgid "Validate VAT Numbers Automatically and Stay Compliant Across Europe. ensuring your customers have a seamless experience while you handle VAT like a pro."
msgstr ""

#: class-products.php:106
msgid "Email Verification for WooCommerce"
msgstr ""

#: class-products.php:107
msgid "Secure Your WooCommerce Store by preventing fake accounts, and ensuring real customers with user-friendly email verification."
msgstr ""

#: class-products.php:116
msgid "Free Shipping Over Amount: Amount Left Tracker for WooCommerce"
msgstr ""

#: class-products.php:117
msgid "Unlock Higher Sales with Free Shipping Incentives."
msgstr ""

#: class-products.php:126
msgid "Payment Methods by Product & Country for WooCommerce"
msgstr ""

#: class-products.php:127
msgid "Control payment methods to keep higher profit, boost conversions, and offer a better checkout experience."
msgstr ""

#: class-products.php:136
msgid "Product XML Feeds for WooCommerce"
msgstr ""

#: class-products.php:137
msgid "Create unlimited product XML feeds using this feature-rich plugin, enabling you to generate, customize, and manage XML feeds based on merchant needs. Compatible with various platforms."
msgstr ""

#: class-products.php:146
msgid "Popup Notices: Added to Cart, Checkout Popups & More"
msgstr ""

#: class-products.php:147
msgid "Capture customer attention with eye-catching, customizable popups messages."
msgstr ""

#: class-products.php:156
msgid "EAN and Barcodes for WooCommerce"
msgstr ""

#: class-products.php:157
msgid "Make Inventory Control a Breeze and Manage Your Products Seamlessly."
msgstr ""

#: class-products.php:166
msgid "MSRP (RRP) Pricing for WooCommerce"
msgstr ""

#: class-products.php:167
msgid "Encourage Purchases by Displaying MSRP and Proving Your Prices Beat the Market."
msgstr ""

#: class-products.php:176
msgid "File Renaming on Upload – WordPress Plugin"
msgstr ""

#: class-products.php:177
msgid "Elevate your WP media management with \"Rename Media Files on Upload for WordPress\" plugin.  Automatically rename media images & files based on rules, sanitizes filenames, and enriches SEO through smart naming conventions."
msgstr ""

#: class-products.php:186
msgid "Coupons & Add to Cart by URL for WooCommerce"
msgstr ""

#: class-products.php:187
msgid "Simplify Shopping with One-Click Coupons and Turn Links into Sales!"
msgstr ""

#: class-products.php:196
msgid "Dynamic Pricing & Bulk Quantity Discounts"
msgstr ""

#: class-products.php:197
msgid "Boost Larger Orders and Maximize Revenue with Dynamic Pricing."
msgstr ""

#: class-products.php:206
msgid "Download Plugins and Themes from Dashboard"
msgstr ""

#: class-products.php:207
msgid "Download your WordPress plugins and themes in ZIP files directly from admin dashboard, get any or all plugins & themes without FTP or cPanel access in a single click."
msgstr ""

#: class-products.php:216
msgid "Back Button Widget - WordPress Plugin"
msgstr ""

#: class-products.php:217
msgid "Simplify navigation on your WordPress site with the \"Back Button Widget\" plugin, a light-weight & user-friendly tool to show a \"Back\" button anywhere on your website."
msgstr ""

#: class-products.php:226
msgid "Slugs Manager: Delete Old Permalinks "
msgstr ""

#: class-products.php:227
msgid "Optimize Your Site Performance by Cleaning Up Old and Unused Permalinks Effortlessly."
msgstr ""

#: class-products.php:236
msgid "Name Your Price: Make a Price Offer for WooCommerce"
msgstr ""

#: class-products.php:237
msgid "A great way to engage shoppers and drive sales through customer-driven pricing."
msgstr ""

#: class-wpfactory-cross-selling.php:104
#: class-wpfactory-cross-selling.php:110
msgid "Recommendations"
msgstr ""

#: class-wpfactory-cross-selling.php:109
msgid "WPFactory Recommendations"
msgstr ""
