/*!****************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[2]!./node_modules/sass-loader/dist/cjs.js!./src/scss/admin.scss ***!
  \****************************************************************************************************************************************************************************************************************/
/**
 * WPFactory Cross-Selling - Admin.
 *
 * @version 1.0.0
 * @since   1.0.0
 * <AUTHOR>
 */
.wpfcs-product.disabled, .wpfcs-button.disabled {
  pointer-events: none;
  opacity: 0.5; /* Safari 6.0 - 9.0 */
  filter: grayscale(100%);
}

.wpfcs-product.readonly {
  opacity: 0.5; /* Safari 6.0 - 9.0 */
  filter: grayscale(100%);
}

.wpfcs-category {
  margin: 33px 0 17px 0;
  font-size: 18px;
  font-weight: 700;
}
@media (max-width: 800px) {
  .wpfcs-category {
    text-align: center;
  }
}

.wpfcs-button {
  border-radius: 100px;
  font-size: 14px;
  padding: 7px 18px;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  display: flex;
  align-items: center; /* Centers items vertically */
  justify-content: center;
  font-weight: 700;
}
.wpfcs-button i {
  margin: 0 8px 0 -3px;
}
.wpfcs-button-1 {
  color: #fff;
  background: #14243B;
}
.wpfcs-button-1 i {
  color: #02AAF2;
}
.wpfcs-button-1:hover {
  background: #204677;
  color: #fff;
}

.wpfcs-button-2 {
  color: #14243B;
  border: 1px solid #DCDCDE;
}
.wpfcs-button-2 i {
  color: #1A2DC9;
}
.wpfcs-button-2:hover {
  background: #f5f5f5;
  color: #14243B;
}

.wpfcs-product {
  background: #FFFFFF;
  padding: 18px 24px;
  border: 1px solid #F2F2F2;
  display: flex;
  justify-content: flex-start; /* Aligns items horizontally (in a row) */
  align-items: center; /* Centers items vertically */
  gap: 18px;
  flex-wrap: nowrap;
}
@media (max-width: 800px) {
  .wpfcs-product {
    flex-wrap: wrap;
    justify-content: center;
  }
}

.wpfcs-product-title {
  margin: 0 0 7px;
}
.wpfcs-product-title a {
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
}
.wpfcs-product-title a:hover {
  color: #135e96;
}

.wpfcs-product-desc {
  margin: 0;
  color: #878787;
}

.wpfcs-product-actions {
  margin-left: auto;
  border-left: 1px solid #F2F2F2;
  padding: 10px 0 10px 24px;
  display: flex;
  justify-content: flex-start; /* Aligns items horizontally (in a row) */
  align-items: center;
  gap: 10px;
  min-width: 239px;
}
@media (max-width: 800px) {
  .wpfcs-product-actions {
    margin-left: 0;
    margin-left: initial;
    padding-left: 0;
    border-left: none;
  }
}

.wpfcs-product-img-wrapper {
  width: 60px;
  height: 60px;
  min-width: 60px;
  background: rgb(255, 255, 255);
  background: linear-gradient(180deg, rgb(255, 255, 255) 0%, rgb(190, 233, 252) 100%);
  display: flex;
  justify-content: center;
  align-items: center;
}
.wpfcs-product-img-wrapper * {
  font-size: 0;
  display: flex;
}

.wpfcs-product-img-wrapper img {
  max-width: 100%;
  max-height: 100%;
  height: auto;
  width: auto;
}
