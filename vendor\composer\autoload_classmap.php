<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'WPFactory\\WPFactory_Admin_Menu\\Singleton' => $vendorDir . '/wpfactory/wpfactory-admin-menu/src/php/trait-singleton.php',
    'WPFactory\\WPFactory_Admin_Menu\\WC_Settings_Menu_Item_Swapper' => $vendorDir . '/wpfactory/wpfactory-admin-menu/src/php/class-wc-settings-menu-item-swapper.php',
    'WPFactory\\WPFactory_Admin_Menu\\WPFactory_Admin_Menu' => $vendorDir . '/wpfactory/wpfactory-admin-menu/src/php/class-wpfactory-admin-menu.php',
    'WPFactory\\WPFactory_Cross_Selling\\Product_Categories' => $vendorDir . '/wpfactory/wpfactory-cross-selling/src/php/class-product-categories.php',
    'WPFactory\\WPFactory_Cross_Selling\\Products' => $vendorDir . '/wpfactory/wpfactory-cross-selling/src/php/class-products.php',
    'WPFactory\\WPFactory_Cross_Selling\\Singleton' => $vendorDir . '/wpfactory/wpfactory-cross-selling/src/php/trait-singleton.php',
    'WPFactory\\WPFactory_Cross_Selling\\WPFactory_Cross_Selling' => $vendorDir . '/wpfactory/wpfactory-cross-selling/src/php/class-wpfactory-cross-selling.php',
);
